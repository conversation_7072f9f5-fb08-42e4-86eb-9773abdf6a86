import React, { useState, useEffect, useRef } from "react";
import { Mo<PERSON>, Tabs, Select, Slider, Switch, Progress } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { PiVideoCameraFill } from "react-icons/pi";
import { ReactComponent as VoiceIcon } from "../settings/icons/VoiceIco.svg";
import { ReactComponent as MicIcon } from "./Assets/microphoneDevice.svg";
import { ReactComponent as SpeakerIcon } from "./Assets/speakerDevice.svg";
import { ReactComponent as CameraIcon } from "./Assets/cameraDevice.svg";
import { ReactComponent as NoiseCancellationIcon } from "./Assets/noiseCancellation.svg";
import { ReactComponent as EchoCancellationIcon } from "./Assets/echoCancellation.svg";
import { ReactComponent as MirrorSelfIcon } from "./Assets/mirrorSelf.svg";
import "./SettingsPrejoin.scss";

const { Option } = Select;

export default function SettingsPrejoin({
  open,
  setOpen,
  // Audio props
  audioDeviceId,
  setAudioDeviceId,
  audioTrack,
  // Video props
  videoDeviceId,
  setVideoDeviceId,
  videoTrack,
  // Speaker props
  speakerDeviceId,
  setSpeakerDeviceId,
  // Settings props
  room,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  brightness = 100,
  onBrightnessChange,
  // Permission props
  permissions = { camera: false, microphone: false },
}) {
  // Device states
  const [audioDevices, setAudioDevices] = useState([]);
  const [videoDevices, setVideoDevices] = useState([]);
  const [speakerDevices, setSpeakerDevices] = useState([]);

  // Audio level states
  const [inputLevel, setInputLevel] = useState(0);
  const [outputLevel, setOutputLevel] = useState(0);
  const [inputVolume, setInputVolume] = useState(100);
  const [outputVolume, setOutputVolume] = useState(100);

  // Audio settings states
  const [noiseCancellation, setNoiseCancellation] = useState(
    room?.options?.audioCaptureDefaults?.noiseSuppression || false
  );
  const [echoCancellation, setEchoCancellation] = useState(
    room?.options?.audioCaptureDefaults?.echoCancellation || false
  );
  const [autoMuteOnJoin, setAutoMuteOnJoin] = useState(true);

  // Audio level monitoring
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const animationFrameRef = useRef(null);

  // Fetch available devices
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();

        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        const videoInputs = devices.filter(device => device.kind === 'videoinput');
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput');

        setAudioDevices(audioInputs);
        setVideoDevices(videoInputs);
        setSpeakerDevices(audioOutputs);
      } catch (error) {
        console.error('Error fetching devices:', error);
      }
    };

    if (open && (permissions.camera || permissions.microphone)) {
      fetchDevices();
    }
  }, [open, permissions]);

  // Audio level monitoring setup
  useEffect(() => {
    if (!audioTrack || !open) return;

    const setupAudioMonitoring = async () => {
      try {
        const stream = audioTrack.mediaStream;
        if (!stream) return;

        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
        const source = audioContextRef.current.createMediaStreamSource(stream);
        analyserRef.current = audioContextRef.current.createAnalyser();

        analyserRef.current.fftSize = 256;
        source.connect(analyserRef.current);

        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);

        const updateLevel = () => {
          if (!analyserRef.current) return;

          analyserRef.current.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
          const level = Math.min(100, (average / 255) * 100);
          setInputLevel(level);

          animationFrameRef.current = requestAnimationFrame(updateLevel);
        };

        updateLevel();
      } catch (error) {
        console.error('Error setting up audio monitoring:', error);
      }
    };

    setupAudioMonitoring();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [audioTrack, open]);

  // Handle device changes
  const handleAudioDeviceChange = (deviceId) => {
    setAudioDeviceId(deviceId);
  };

  const handleVideoDeviceChange = (deviceId) => {
    setVideoDeviceId(deviceId);
  };

  const handleSpeakerDeviceChange = (deviceId) => {
    if (setSpeakerDeviceId) {
      setSpeakerDeviceId(deviceId);
    }
  };

  // Handle audio settings changes
  const handleNoiseCancellation = (checked) => {
    setNoiseCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.noiseSuppression = checked;
    }
  };

  const handleEchoCancellation = (checked) => {
    setEchoCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.echoCancellation = checked;
    }
  };

  // Test functions
  const testMicrophone = () => {
    // Trigger a brief audio level spike for visual feedback
    setInputLevel(80);
    setTimeout(() => setInputLevel(0), 1000);
  };

  const testSpeaker = () => {
    // Play a test sound or trigger output level for visual feedback
    setOutputLevel(80);
    setTimeout(() => setOutputLevel(0), 1000);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const getDeviceName = (devices, deviceId, defaultName) => {
    const device = devices.find(d => d.deviceId === deviceId);
    return device?.label || defaultName;
  };

  const truncateDeviceName = (name, maxLength = 30) => {
    return name.length > maxLength ? `${name.slice(0, maxLength)}...` : name;
  };

  // Tab items configuration
  const tabItems = [
    {
      key: 'audio',
      label: (
        <div className="tab-label">
          <VoiceIcon />
          <span>Audio</span>
        </div>
      ),
      children: (
        <div className="settings-content">
          <div className="settings-section">
            <h3>Audio Settings</h3>
            <p className="settings-description">Change your audio settings here</p>
          </div>

          {/* Microphone Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <MicIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Microphone</span>
                  <span className="setting-sublabel">Choose Microphone</span>
                </div>
              </div>
              <div className="setting-control">
                <Select
                  value={audioDeviceId}
                  onChange={handleAudioDeviceChange}
                  style={{ width: 200 }}
                  placeholder="Select microphone"
                  disabled={!permissions.microphone}
                >
                  {audioDevices.map(device => (
                    <Option key={device.deviceId} value={device.deviceId}>
                      {truncateDeviceName(device.label || 'Default Microphone')}
                    </Option>
                  ))}
                </Select>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <span className="setting-label">Test Mic</span>
              </div>
              <div className="setting-control">
                <button
                  className="test-button"
                  onClick={testMicrophone}
                  disabled={!permissions.microphone}
                >
                  Test Mic
                </button>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <span className="setting-label">Input Level:</span>
              </div>
              <div className="setting-control">
                <div className="level-indicator">
                  <Progress
                    percent={inputLevel}
                    showInfo={false}
                    strokeColor="#52c41a"
                    size="small"
                  />
                </div>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <span className="setting-label">Input Volume:</span>
              </div>
              <div className="setting-control">
                <Slider
                  value={inputVolume}
                  onChange={setInputVolume}
                  style={{ width: 200 }}
                  min={0}
                  max={100}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
              </div>
            </div>
          </div>

          {/* Speaker Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <SpeakerIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Speaker</span>
                  <span className="setting-sublabel">Choose Speaker</span>
                </div>
              </div>
              <div className="setting-control">
                <Select
                  value={speakerDeviceId}
                  onChange={handleSpeakerDeviceChange}
                  style={{ width: 200 }}
                  placeholder="Select speaker"
                  disabled={!permissions.microphone}
                >
                  {speakerDevices.map(device => (
                    <Option key={device.deviceId} value={device.deviceId}>
                      {truncateDeviceName(device.label || 'Default Speaker')}
                    </Option>
                  ))}
                </Select>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <span className="setting-label">Test Speaker</span>
              </div>
              <div className="setting-control">
                <button
                  className="test-button"
                  onClick={testSpeaker}
                  disabled={!permissions.microphone}
                >
                  Test Speaker
                </button>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <span className="setting-label">Output Level:</span>
              </div>
              <div className="setting-control">
                <div className="level-indicator">
                  <Progress
                    percent={outputLevel}
                    showInfo={false}
                    strokeColor="#52c41a"
                    size="small"
                  />
                </div>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <span className="setting-label">Output Volume:</span>
              </div>
              <div className="setting-control">
                <Slider
                  value={outputVolume}
                  onChange={setOutputVolume}
                  style={{ width: 200 }}
                  min={0}
                  max={100}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
              </div>
            </div>
          </div>

          {/* Audio Enhancement Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <NoiseCancellationIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Noise cancellation</span>
                  <span className="setting-sublabel">Remove background noise to improve call quality</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={noiseCancellation}
                  onChange={handleNoiseCancellation}
                />
                <span className="switch-status">
                  {noiseCancellation ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <EchoCancellationIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Echo cancellation</span>
                  <span className="setting-sublabel">Remove background noise to improve call quality</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={echoCancellation}
                  onChange={handleEchoCancellation}
                />
                <span className="switch-status">
                  {echoCancellation ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <div className="setting-details">
                  <span className="setting-label">Auto-mute on joining meeting</span>
                  <span className="setting-sublabel">Remove background noise to improve call quality</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={autoMuteOnJoin}
                  onChange={setAutoMuteOnJoin}
                />
                <span className="switch-status">
                  {autoMuteOnJoin ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'video',
      label: (
        <div className="tab-label">
          <PiVideoCameraFill />
          <span>Video</span>
        </div>
      ),
      children: (
        <div className="settings-content">
          <div className="settings-section">
            <h3>Video Settings</h3>
            <p className="settings-description">Change your video settings here</p>
          </div>

          {/* Camera Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <CameraIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Camera</span>
                  <span className="setting-sublabel">Choose Camera</span>
                </div>
              </div>
              <div className="setting-control">
                <Select
                  value={videoDeviceId}
                  onChange={handleVideoDeviceChange}
                  style={{ width: 200 }}
                  placeholder="Select camera"
                  disabled={!permissions.camera}
                >
                  {videoDevices.map(device => (
                    <Option key={device.deviceId} value={device.deviceId}>
                      {truncateDeviceName(device.label || 'Default Camera')}
                    </Option>
                  ))}
                </Select>
              </div>
            </div>
          </div>

          {/* Video Enhancement Section */}
          <div className="settings-section">
            <div className="setting-row">
              <div className="setting-info">
                <MirrorSelfIcon className="setting-icon" />
                <div className="setting-details">
                  <span className="setting-label">Mirror video</span>
                  <span className="setting-sublabel">Flip your video horizontally to correct the orientation</span>
                </div>
              </div>
              <div className="setting-control">
                <Switch
                  checked={isSelfVideoMirrored}
                  onChange={setIsSelfVideoMirrored}
                />
                <span className="switch-status">
                  {isSelfVideoMirrored ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            <div className="setting-row">
              <div className="setting-info">
                <div className="setting-details">
                  <span className="setting-label">Video brightness</span>
                  <span className="setting-sublabel">Adjust the brightness of your video to improve visibility</span>
                </div>
              </div>
              <div className="setting-control">
                <Slider
                  value={brightness}
                  onChange={onBrightnessChange}
                  style={{ width: 200 }}
                  min={50}
                  max={150}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
                <span className="brightness-value">{brightness}%</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <Modal
      open={open}
      onCancel={handleClose}
      className="settings-prejoin-modal"
      footer={null}
      width="60vw"
      style={{ maxWidth: '800px' }}
      closeIcon={<CloseOutlined />}
      centered
      title="Settings"
    >
      <Tabs
        defaultActiveKey="audio"
        items={tabItems}
        tabPosition="left"
        className="settings-tabs"
      />
    </Modal>
  );
}